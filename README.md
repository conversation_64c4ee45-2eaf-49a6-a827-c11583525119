# BeeSwarm 鱼眼摄像头系统

这是一个完整的鱼眼摄像头标定、校正和管理系统，专为BeeSwarm机器人项目设计。

## 📁 项目结构

```
BeeSwarm/
├── camera_tools/           # 摄像头工具集
│   ├── core/              # 核心摄像头工具
│   │   ├── fisheye_camera_tools.py    # 鱼眼摄像头专用工具 ⭐
│   │   ├── camera_demo.py             # 通用摄像头演示
│   │   ├── camera_manager.py          # 摄像头管理器
│   │   ├── simple_camera_viewer.py    # 简单查看器
│   │   └── batch_capture.py           # 批量拍摄工具
│   ├── calibration/       # 标定工具
│   │   ├── fisheye_calibration.py     # 主要标定程序
│   │   ├── fisheye_calibration_manager.py
│   │   ├── fisheye_calibration_interactive.py
│   │   ├── chessboard_detector.py     # 棋盘格检测器
│   │   └── debug_chessboard.py
│   ├── correction/        # 校正工具
│   │   ├── fisheye_correction.py      # 基础校正
│   │   └── fisheye_correction_advanced.py  # 高级校正
│   └── utils/             # 工具程序
│       ├── camera_identifier.py
│       └── calibration_image_filter.py
├── data/                  # 数据存储
│   ├── calibration_images/    # 标定图像
│   │   ├── fisheye_calibration/
│   │   ├── fisheye_calibration_backup/
│   │   └── fisheye_calibration_filtered/
│   ├── calibration_results/   # 标定结果
│   ├── captured_images/       # 拍摄的图像
│   ├── corrected_images/      # 校正后图像
│   └── camera_identification/ # 摄像头识别数据
├── scripts/               # 脚本工具
│   ├── quick_start/       # 快速启动
│   │   └── fisheye_quick_start.py
│   └── maintenance/       # 维护脚本
│       ├── diagnose_usb_cameras.sh
│       ├── fix_camera_access.sh
│       └── recover_cameras.sh
├── docs/                  # 文档
│   ├── CAMERA_USAGE.md
│   └── FISHEYE_CALIBRATION_GUIDE.md
└── README.md             # 本文件
```

## 🚀 快速开始

### 1. 实时查看所有鱼眼摄像头
```bash
python3 camera_tools/core/fisheye_camera_tools.py --live-grid
```

### 2. 使用快速启动菜单
```bash
python3 scripts/quick_start/fisheye_quick_start.py
```

### 3. 进行鱼眼标定
```bash
# 拍摄标定图像
python3 camera_tools/calibration/fisheye_calibration.py --camera 0 --capture --num-images 25

# 执行标定
python3 camera_tools/calibration/fisheye_calibration.py --camera 0 --calibrate
```

## 🎯 主要功能

### 📸 摄像头操作
- **实时查看**: 支持单个或多个鱼眼摄像头同时显示
- **批量拍摄**: 支持延时拍摄、全景拍摄
- **网格显示**: 2x2网格显示4个鱼眼摄像头

### 🔧 鱼眼标定
- **棋盘格检测**: 自动检测棋盘格尺寸
- **精确标定**: 基于OpenCV Kannala-Brandt模型
- **质量评估**: 重投影误差评估

### 🎨 图像校正
- **畸变校正**: 去除鱼眼畸变
- **多种模式**: 标准、平衡、裁剪校正
- **实时处理**: 支持实时校正预览

## 🛠️ 系统信息

**检测到的鱼眼摄像头:**
- Camera 0: /dev/video0 (前方鱼眼)
- Camera 2: /dev/video2 (右侧鱼眼)  
- Camera 4: /dev/video4 (后方鱼眼)
- Camera 6: /dev/video6 (左侧鱼眼)

**分辨率**: 1280x720 @ 10fps
**视野角度**: >180度广角

## 📖 详细文档

- [摄像头使用指南](docs/CAMERA_USAGE.md)
- [鱼眼标定完整指南](docs/FISHEYE_CALIBRATION_GUIDE.md)

## 🔧 故障排除

### 摄像头被占用
```bash
# 停止ROS节点
pkill -f usb_cam
pkill -f roscore

# 重启摄像头硬件
sudo rmmod uvcvideo
sudo modprobe uvcvideo
```

### 权限问题
```bash
sudo usermod -a -G video $USER
# 然后重新登录
```

### 诊断摄像头
```bash
bash scripts/maintenance/diagnose_usb_cameras.sh
```

## 🎮 便捷启动脚本

为了方便使用，我们提供了一个便捷的启动脚本，您可以直接运行：

```bash
./start_camera_tools.sh
```

这个脚本会自动检测您的需求并启动相应的工具。

---

**开发团队**: BeeSwarm Project  
**最后更新**: 2025-08-18
