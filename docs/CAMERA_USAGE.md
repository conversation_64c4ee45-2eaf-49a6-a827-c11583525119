# 鱼眼摄像头使用指南

本指南介绍如何在BeeSwarm系统中直接查看和使用4个鱼眼摄像头，支持360度全景视野。

## 系统信息

**检测到的鱼眼摄像头设备:**
- Camera 0: /dev/video0 (1280x720 @ 10fps) - 前方鱼眼
- Camera 2: /dev/video2 (1280x720 @ 10fps) - 右侧鱼眼
- Camera 4: /dev/video4 (1280x720 @ 10fps) - 后方鱼眼
- Camera 6: /dev/video6 (1280x720 @ 10fps) - 左侧鱼眼

**注意:** video1, video3, video5, video7 是元数据设备，不用于图像捕获。

## 鱼眼摄像头特点

- **360度视野覆盖**: 4个鱼眼摄像头提供完整的周围环境视野
- **广角视野**: 每个鱼眼摄像头具有超广角视野 (通常>180度)
- **畸变特性**: 鱼眼图像具有特殊的球面畸变，需要校正处理
- **全景应用**: 适合机器人导航、监控、VR/AR应用

## 可用工具

### 1. 鱼眼摄像头专用工具 (`fisheye_camera_tools.py`) ⭐ 推荐

专门为鱼眼摄像头设计的工具，支持网格显示、全景拍摄等功能。

#### 基本用法:
```bash
# 拍摄所有鱼眼摄像头
python3 fisheye_camera_tools.py --all

# 拍摄单个鱼眼摄像头
python3 fisheye_camera_tools.py --single 0

# 创建2x2鱼眼网格图像
python3 fisheye_camera_tools.py --grid

# 实时鱼眼网格显示 (推荐!)
python3 fisheye_camera_tools.py --live-grid

# 创建全景序列 (数量 间隔)
python3 fisheye_camera_tools.py --panorama 10 2

# 交互式菜单
python3 fisheye_camera_tools.py
```

#### 实时网格查看控制:
- **q**: 退出
- **s**: 保存当前网格图像
- **c**: 保存所有单独的摄像头图像

### 2. 鱼眼图像校正工具 (`fisheye_correction.py`)

专门处理鱼眼图像畸变校正和增强。

```bash
# 鱼眼去畸变处理
python3 fisheye_correction.py --dir fisheye_captures --type undistort

# 创建透视视图
python3 fisheye_correction.py --dir fisheye_captures --type perspective

# 图像增强处理
python3 fisheye_correction.py --dir fisheye_captures --type enhanced

# 全部处理
python3 fisheye_correction.py --dir fisheye_captures --type all

# 处理单个图像
python3 fisheye_correction.py --image path/to/fisheye.jpg --type undistort
```

### 3. 完整功能Demo (`camera_demo.py`)

通用camera管理工具，也支持鱼眼摄像头。

#### 基本用法:
```bash
# 检测可用camera
python3 camera_demo.py --detect

# 实时查看camera (按q退出，s保存，c连续保存)
python3 camera_demo.py --live 0

# 拍摄单张照片
python3 camera_demo.py --photo 0

# 批量拍摄 (camera_id, 数量)
python3 camera_demo.py --batch 0 5

# 测试所有camera
python3 camera_demo.py --test-all

# 交互式菜单 (默认)
python3 camera_demo.py
```

#### 实时查看控制:
- **q**: 退出
- **s**: 保存当前帧
- **c**: 切换连续保存模式 (每秒保存一张)

### 2. 简单实时查看器 (`simple_camera_viewer.py`)

轻量级的实时camera查看工具。

```bash
# 查看指定camera
python3 simple_camera_viewer.py 0
python3 simple_camera_viewer.py 2
python3 simple_camera_viewer.py 4
python3 simple_camera_viewer.py 6
```

#### 控制:
- **q**: 退出
- **s**: 保存当前帧

### 3. 批量拍摄工具 (`batch_capture.py`)

专门用于批量拍摄和延时摄影。

```bash
# 拍摄所有camera
python3 batch_capture.py --all

# 拍摄指定camera
python3 batch_capture.py --camera 0

# 延时拍摄 (camera_id, 张数, 间隔秒数)
python3 batch_capture.py --timelapse 0 10 2

# 指定保存目录和文件前缀
python3 batch_capture.py --all --dir my_photos --prefix "test"
```

## 鱼眼摄像头使用示例

### 🚀 快速开始 - 实时查看所有鱼眼摄像头
```bash
cd /home/<USER>/BeeSwarm
python3 fisheye_camera_tools.py --live-grid
```

### 📸 拍摄360度全景照片
```bash
# 拍摄所有鱼眼摄像头
python3 fisheye_camera_tools.py --all --prefix "panorama"

# 创建鱼眼网格图像
python3 fisheye_camera_tools.py --grid
```

### 🎬 创建全景延时序列
```bash
# 创建10组全景照片，每2秒一组
python3 fisheye_camera_tools.py --panorama 10 2
```

### 🔧 鱼眼图像校正
```bash
# 校正鱼眼畸变
python3 fisheye_correction.py --dir fisheye_captures --type undistort

# 创建透视视图
python3 fisheye_correction.py --dir fisheye_captures --type perspective
```

### 📊 测试所有摄像头
```bash
python3 camera_demo.py --test-all
```

### 🎯 单个摄像头操作
```bash
# 实时查看单个鱼眼摄像头
python3 simple_camera_viewer.py 0

# 拍摄单个鱼眼摄像头
python3 fisheye_camera_tools.py --single 0
```

## 文件保存

### 默认保存位置:
- `fisheye_camera_tools.py`: `fisheye_captures/`
- `fisheye_correction.py`: `fisheye_corrected/`
- `camera_demo.py`: `camera_captures/`
- `simple_camera_viewer.py`: 当前目录
- `batch_capture.py`: `captures/` (可自定义)

### 鱼眼文件命名格式:
- 单张鱼眼照片: `fisheye_{id}_{timestamp}.jpg`
- 鱼眼网格图像: `fisheye_grid_{timestamp}.jpg`
- 全景序列: `seq_{序号}_fisheye_{id}_{timestamp}.jpg`
- 校正后图像: `{原文件名}_undistorted_{timestamp}.jpg`
- 中心区域: `{原文件名}_center_{timestamp}.jpg`
- 透视视图: `{原文件名}_perspective_{timestamp}.jpg`

### 传统文件命名格式:
- 单张照片: `camera_{id}_{timestamp}.jpg`
- 批量照片: `{prefix}_camera_{id}_{timestamp}.jpg`
- 延时照片: `timelapse_camera_{id}_{序号}_{timestamp}.jpg`

## 故障排除

### 1. Camera被占用
如果看到 "Device is busy" 错误，可能是ROS或其他程序在使用camera:
```bash
# 停止ROS节点
pkill -f usb_cam
pkill -f roscore

# 或重启camera硬件
sudo rmmod uvcvideo
sudo modprobe uvcvideo
```

### 2. 权限问题
确保用户在video组中:
```bash
sudo usermod -a -G video $USER
# 然后重新登录
```

### 3. 检查camera状态
```bash
# 查看camera设备
ls -l /dev/video*

# 查看camera信息
v4l2-ctl --list-devices

# 测试camera
v4l2-ctl --device=/dev/video0 --all
```

## 性能优化

### 1. 减少延迟
- 使用较低的分辨率 (640x480)
- 减少缓冲帧数
- 使用MJPEG格式

### 2. 批量操作
- 使用批量拍摄工具而不是循环调用单张拍摄
- 在camera之间添加短暂延迟避免冲突

### 3. 存储优化
- 定期清理旧照片
- 使用压缩格式 (JPEG质量90-95)
- 考虑使用外部存储设备

## 集成到BeeSwarm系统

这些工具可以轻松集成到现有的BeeSwarm系统中:

1. **作为独立模块**: 直接调用脚本进行camera操作
2. **集成到web界面**: 通过Flask路由调用camera功能
3. **ROS节点包装**: 将功能包装为ROS服务
4. **MQTT发布**: 将拍摄的照片通过MQTT发布

## 下一步

1. 根据需要修改分辨率和帧率设置
2. 添加图像处理功能 (滤镜、标注等)
3. 实现自动拍摄调度
4. 添加云存储上传功能
5. 集成到现有的机器人控制系统

---

**提示**: 所有脚本都包含详细的帮助信息，使用 `--help` 参数查看完整选项。
